import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
from typing import Tuple, Optional

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def plot_charging_load(fcs_file_path: str = 'results/std_37nodes/csv_exports/fcs.csv',
                      scs_file_path: str = 'results/std_37nodes/csv_exports/scs.csv',
                      save_path: Optional[str] = None, show_plot: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:

    # 验证文件存在性
    if not Path(fcs_file_path).exists():
        raise FileNotFoundError(f"快充站文件不存在: {fcs_file_path}")
    if not Path(scs_file_path).exists():
        raise FileNotFoundError(f"慢充站文件不存在: {scs_file_path}")

    # 读取数据文件
    try:
        fcs_df = pd.read_csv(fcs_file_path)
        scs_df = pd.read_csv(scs_file_path)
    except Exception as e:
        raise Exception(f"读取数据文件失败: {e}")

    # 数据预处理和时间对齐
    time_hours, fcs_power_kw, scs_power_kw, fcs_count, scs_count = _process_charging_data(fcs_df, scs_df)

    # 单位转换 (kW -> MW)
    total_power_mw = (fcs_power_kw + scs_power_kw) / 1000.0
    fcs_power_mw = fcs_power_kw / 1000.0
    scs_power_mw = scs_power_kw / 1000.0

    # 计算总车辆数量
    total_count = fcs_count + scs_count

    # 创建图表
    fig, (ax1, ax2) = _create_charging_plot(time_hours, total_power_mw, fcs_power_mw, scs_power_mw, total_count, fcs_count, scs_count)

    # 保存和显示
    if save_path:
        _save_plot(fig, save_path)

    if show_plot:
        plt.show()

    return time_hours, total_power_mw, fcs_power_mw, scs_power_mw, total_count, fcs_count, scs_count


def _process_charging_data(fcs_df: pd.DataFrame, scs_df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """处理充电数据，对齐时间序列，提取功率和车辆数量数据"""

    # 验证时间列
    if 'Time_hours' not in fcs_df.columns or 'Time_hours' not in scs_df.columns:
        raise ValueError("数据文件缺少Time_hours列")

    # 找到共同时间点
    fcs_times = set(fcs_df['Time_hours'].values)
    scs_times = set(scs_df['Time_hours'].values)
    common_times = sorted(fcs_times.intersection(scs_times))

    # 筛选和排序数据
    fcs_filtered = fcs_df[fcs_df['Time_hours'].isin(common_times)].sort_values('Time_hours').reset_index(drop=True)
    scs_filtered = scs_df[scs_df['Time_hours'].isin(common_times)].sort_values('Time_hours').reset_index(drop=True)

    # 提取时间数据
    time_hours = fcs_filtered['Time_hours'].values

    # 提取快充功率数据（#c列：充电功率kW）
    fcs_power_columns = [col for col in fcs_filtered.columns if col.endswith('#c')]
    fcs_total_power_kw = fcs_filtered[fcs_power_columns].sum(axis=1).values

    # 提取快充车辆数量数据（#cnt列：充电站内总车辆数量）
    fcs_count_columns = [col for col in fcs_filtered.columns if col.endswith('#cnt')]
    fcs_total_count = fcs_filtered[fcs_count_columns].sum(axis=1).values

    # 提取慢充车辆数量数据（#cnt列：充电站内总车辆数量，包括充电完成但未离开的车辆）
    scs_count_columns = [col for col in scs_filtered.columns if col.endswith('#cnt')]
    scs_total_count = scs_filtered[scs_count_columns].sum(axis=1).values

    # 慢充功率数据（原始代码中慢充功率就是基于#cnt列计算的）
    scs_total_power_kw = scs_filtered[scs_count_columns].sum(axis=1).values

    return time_hours, fcs_total_power_kw, scs_total_power_kw, fcs_total_count, scs_total_count


def _create_charging_plot(time_hours: np.ndarray, total_power: np.ndarray,
                         fcs_power: np.ndarray, scs_power: np.ndarray,
                         total_count: np.ndarray, fcs_count: np.ndarray, scs_count: np.ndarray):
    """创建充电负荷和车辆数量双子图"""

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12), sharex=True)

    # 上图：充电负荷
    ax1.plot(time_hours, total_power,
            linewidth=3,
            color='#E74C3C',
            marker='o',
            markersize=4,
            markerfacecolor='#C0392B',
            markeredgecolor='white',
            markeredgewidth=0.8,
            alpha=0.9,
            label='总充电负荷')

    ax1.plot(time_hours, fcs_power,
            linewidth=2,
            color='#3498DB',
            marker='s',
            markersize=3,
            alpha=0.7,
            label='快充负荷')

    ax1.plot(time_hours, scs_power,
            linewidth=2,
            color='#2ECC71',
            marker='^',
            markersize=3,
            alpha=0.7,
            label='慢充负荷')

    ax1.set_title('电动汽车充电负荷图\nElectric Vehicle Charging Load',
                 fontsize=18, fontweight='bold', pad=25)
    ax1.set_ylabel('充电功率 (MW)', fontsize=15, fontweight='bold')
    ax1.grid(True, linestyle='-', alpha=0.3, color='gray')
    ax1.set_axisbelow(True)

    power_range = total_power.max() - total_power.min()
    y_margin = max(power_range * 0.1, 0.01)
    y_min = max(0, total_power.min() - y_margin)
    ax1.set_ylim(y_min, total_power.max() + y_margin)
    ax1.legend(loc='upper left', fontsize=13, framealpha=0.9, shadow=True)

    # 下图：充电站内车辆数量
    ax2.plot(time_hours, total_count,
            linewidth=3,
            color='#9B59B6',
            marker='o',
            markersize=4,
            markerfacecolor='#8E44AD',
            markeredgecolor='white',
            markeredgewidth=0.8,
            alpha=0.9,
            label='总车辆数量')

    ax2.plot(time_hours, fcs_count,
            linewidth=2,
            color='#F39C12',
            marker='s',
            markersize=3,
            alpha=0.7,
            label='快充站车辆')

    ax2.plot(time_hours, scs_count,
            linewidth=2,
            color='#1ABC9C',
            marker='^',
            markersize=3,
            alpha=0.7,
            label='慢充站车辆')

    ax2.set_title('充电站内车辆数量\nVehicles in Charging Stations',
                 fontsize=16, fontweight='bold', pad=20)
    ax2.set_xlabel('时间 (小时)', fontsize=15, fontweight='bold')
    ax2.set_ylabel('车辆数量 (辆)', fontsize=15, fontweight='bold')
    ax2.grid(True, linestyle='-', alpha=0.3, color='gray')
    ax2.set_axisbelow(True)

    count_range = total_count.max() - total_count.min()
    y_margin = max(count_range * 0.1, 1)
    y_min = max(0, total_count.min() - y_margin)
    ax2.set_ylim(y_min, total_count.max() + y_margin)
    ax2.legend(loc='upper left', fontsize=13, framealpha=0.9, shadow=True)

    # 设置共同的x轴范围和刻度
    for ax in [ax1, ax2]:
        ax.set_xlim(0, max(time_hours) + 1)
        x_ticks = np.arange(0, max(time_hours) + 6, 6)
        ax.set_xticks(x_ticks)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1.2)
        ax.spines['bottom'].set_linewidth(1.2)

    plt.tight_layout()

    return fig, (ax1, ax2)


def _save_plot(fig, save_path: str):
    """保存图表"""

    try:
        fig.savefig(save_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"图片已保存: {save_path}")
    except Exception as e:
        print(f"保存图片失败: {e}")


if __name__ == '__main__':
    # 运行充电负荷绘图程序
    try:
        # 设置保存路径
        save_path = 'charging_load_plot.png'

        time_hours, total_power, fcs_power, scs_power, total_count, fcs_count, scs_count = plot_charging_load(
            save_path=save_path,
            show_plot=True
        )
        print(f"成功绘制充电负荷和车辆数量图")
        print(f"时间范围: {time_hours[0]:.1f} - {time_hours[-1]:.1f} 小时")
        print(f"总负荷峰值: {total_power.max():.2f} MW")
        print(f"快充负荷峰值: {fcs_power.max():.2f} MW")
        print(f"慢充负荷峰值: {scs_power.max():.2f} MW")
        print(f"充电站内总车辆峰值: {total_count.max():.0f} 辆")
        print(f"快充站车辆峰值: {fcs_count.max():.0f} 辆")
        print(f"慢充站车辆峰值: {scs_count.max():.0f} 辆")
    except Exception as e:
        print(f"程序运行失败: {e}")
        print("请检查数据文件是否存在于 results/std_37nodes/csv_exports/ 目录中")
